using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MarketDataService.Interfaces;
using MarketDataService.Models;
using System.Security.Claims;

namespace MarketDataService.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WatchlistController(
    IWatchlistService watchlistService,
    IBulkPriceService bulkPriceService,
    ILogger<WatchlistController> logger) : ControllerBase
{
    private readonly IWatchlistService _watchlistService = watchlistService;
    private readonly IBulkPriceService _bulkPriceService = bulkPriceService;
    private readonly ILogger<WatchlistController> _logger = logger;

    /// <summary>
    /// Test JWT authentication (requires valid token)
    /// </summary>
    [HttpGet("test-auth")]
    public ActionResult TestAuth()
    {
        try
        {
            var userId = GetUserId();
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();

            return Ok(new
            {
                status = "authenticated",
                message = "JWT token validation successful",
                userId = userId,
                claims = claims,
                timestamp = DateTime.UtcNow,
                service = "MarketData"
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                error = "Authentication Failed",
                message = ex.Message,
                details = "JWT token validation failed",
                timestamp = DateTime.UtcNow,
                service = "MarketData"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in test-auth endpoint");
            return StatusCode(500, new
            {
                error = "Internal Server Error",
                message = "Authentication test failed",
                details = ex.Message,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Test database connection (no auth required)
    /// </summary>
    [HttpGet("test-connection")]
    [AllowAnonymous]
    public async Task<ActionResult> TestConnection()
    {
        try
        {
            // First test basic database connectivity
            var canConnect = await _watchlistService.TestDatabaseConnectionAsync();
            if (!canConnect)
            {
                return StatusCode(500, new {
                    status = "error",
                    message = "Cannot connect to database"
                });
            }

            // Test with a valid GUID format for user ID
            var testUserIdGuid = Guid.Parse("123e4567-e89b-12d3-a456-************");
            var watchlists = await _watchlistService.GetUserWatchlistsAsync(testUserIdGuid);
            return Ok(new {
                status = "connected",
                message = "Successfully connected to Supabase database",
                watchlistCount = watchlists.Count,
                testUserId = testUserIdGuid.ToString()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return StatusCode(500, new {
                status = "error",
                message = "Failed to connect to database",
                error = ex.Message,
                stackTrace = ex.StackTrace
            });
        }
    }

    /// <summary>
    /// Get all watchlists for the authenticated user
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<WatchlistDto>>> GetWatchlists()
    {
        try
        {
            var userId = GetUserIdAsGuid();
            var watchlists = await _watchlistService.GetUserWatchlistsAsync(userId);
            return Ok(watchlists);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Authentication failed for GetWatchlists");
            return Unauthorized(new
            {
                error = "Authentication Required",
                message = ex.Message,
                details = "Please ensure you have a valid JWT token from the auth service",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting watchlists");
            return StatusCode(500, new
            {
                error = "Internal Server Error",
                message = "Failed to get watchlists",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Get a specific watchlist by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<WatchlistDto>> GetWatchlist(long id)
    {
        try
        {
            var userId = GetUserIdAsGuid();
            var watchlist = await _watchlistService.GetWatchlistAsync(userId, id);

            if (watchlist == null)
                return NotFound(new
                {
                    error = "Not Found",
                    message = $"Watchlist {id} not found",
                    timestamp = DateTime.UtcNow
                });

            return Ok(watchlist);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Authentication failed for GetWatchlist {WatchlistId}", id);
            return Unauthorized(new
            {
                error = "Authentication Required",
                message = ex.Message,
                details = "Please ensure you have a valid JWT token from the auth service",
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting watchlist {WatchlistId}", id);
            return StatusCode(500, new
            {
                error = "Internal Server Error",
                message = "Failed to get watchlist",
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Get a watchlist with live prices for all symbols
    /// </summary>
    [HttpGet("{id}/with-prices")]
    public async Task<ActionResult<WatchlistWithPricesDto>> GetWatchlistWithPrices(long id)
    {
        try
        {
            var userId = GetUserIdAsGuid();
            var watchlistWithPrices = await _bulkPriceService.GetWatchlistWithPricesAsync(id, userId);
            return Ok(watchlistWithPrices);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting watchlist {WatchlistId} with prices", id);
            return StatusCode(500, "Failed to get watchlist with prices");
        }
    }

    /// <summary>
    /// Create a new watchlist
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<WatchlistDto>> CreateWatchlist([FromBody] CreateWatchlistRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Name))
                return BadRequest("Watchlist name is required");

            var userId = GetUserIdAsGuid();
            var watchlist = await _watchlistService.CreateWatchlistAsync(userId, request);
            return CreatedAtAction(nameof(GetWatchlist), new { id = watchlist.Id }, watchlist);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating watchlist");
            return StatusCode(500, "Failed to create watchlist");
        }
    }

    /// <summary>
    /// Update an existing watchlist
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<WatchlistDto>> UpdateWatchlist(long id, [FromBody] UpdateWatchlistRequest request)
    {
        try
        {
            var userId = GetUserIdAsGuid();
            var watchlist = await _watchlistService.UpdateWatchlistAsync(userId, id, request);

            if (watchlist == null)
                return NotFound($"Watchlist {id} not found");

            return Ok(watchlist);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating watchlist {WatchlistId}", id);
            return StatusCode(500, "Failed to update watchlist");
        }
    }

    /// <summary>
    /// Delete a watchlist
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteWatchlist(long id)
    {
        try
        {
            var userId = GetUserIdAsGuid();
            var deleted = await _watchlistService.DeleteWatchlistAsync(userId, id);

            if (!deleted)
                return NotFound($"Watchlist {id} not found");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting watchlist {WatchlistId}", id);
            return StatusCode(500, "Failed to delete watchlist");
        }
    }

    /// <summary>
    /// Add symbols to a watchlist
    /// </summary>
    [HttpPost("{id}/symbols")]
    public async Task<ActionResult<WatchlistDto>> AddSymbols(long id, [FromBody] AddSymbolsRequest request)
    {
        try
        {
            if (request.Symbols == null || !request.Symbols.Any())
                return BadRequest("At least one symbol is required");

            var userId = GetUserIdAsGuid();
            var watchlist = await _watchlistService.AddSymbolsAsync(userId, id, request);

            if (watchlist == null)
                return NotFound($"Watchlist {id} not found");

            return Ok(watchlist);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding symbols to watchlist {WatchlistId}", id);
            return StatusCode(500, "Failed to add symbols");
        }
    }

    /// <summary>
    /// Remove a symbol from a watchlist
    /// </summary>
    [HttpDelete("{id}/symbols/{symbol}")]
    public async Task<ActionResult<WatchlistDto>> RemoveSymbol(long id, string symbol)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(symbol))
                return BadRequest("Symbol is required");

            var userId = GetUserIdAsGuid();
            var removed = await _watchlistService.RemoveSymbolAsync(userId, id, symbol);

            if (!removed)
                return NotFound($"Symbol {symbol} not found in watchlist {id}");

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing symbol {Symbol} from watchlist {WatchlistId}", symbol, id);
            return StatusCode(500, "Failed to remove symbol");
        }
    }

    /// <summary>
    /// Reorder items in a watchlist
    /// </summary>
    [HttpPut("{id}/reorder")]
    public async Task<ActionResult<WatchlistDto>> ReorderItems(long id, [FromBody] ReorderItemsRequest request)
    {
        try
        {
            if (request.Items == null || !request.Items.Any())
                return BadRequest("Items are required for reordering");

            var userId = GetUserIdAsGuid();
            var watchlist = await _watchlistService.ReorderItemsAsync(userId, id, request);

            if (watchlist == null)
                return NotFound($"Watchlist {id} not found");

            return Ok(watchlist);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering items in watchlist {WatchlistId}", id);
            return StatusCode(500, "Failed to reorder items");
        }
    }

    /// <summary>
    /// Get bulk prices for multiple symbols
    /// </summary>
    [HttpPost("bulk-prices")]
    public async Task<ActionResult<BulkPriceResponse>> GetBulkPrices([FromBody] BulkPriceRequest request)
    {
        try
        {
            if (request.Symbols == null || !request.Symbols.Any())
                return BadRequest("At least one symbol is required");

            if (request.Symbols.Count > 100)
                return BadRequest("Maximum 100 symbols allowed per request");

            var response = await _bulkPriceService.GetBulkPricesAsync(request.Symbols, null, request.BypassCache);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bulk prices");
            return StatusCode(500, "Failed to get bulk prices");
        }
    }

    /// <summary>
    /// Get price analytics for multiple symbols
    /// </summary>
    [HttpPost("price-analytics")]
    public async Task<ActionResult<List<PriceAnalyticsDto>>> GetPriceAnalytics([FromBody] BulkSymbolRequest request)
    {
        try
        {
            if (request.Symbols == null || !request.Symbols.Any())
                return BadRequest("At least one symbol is required");

            if (request.Symbols.Count > 50)
                return BadRequest("Maximum 50 symbols allowed per request");

            var analytics = await _bulkPriceService.GetPriceAnalyticsAsync(request.Symbols, request.BrokerId);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting price analytics");
            return StatusCode(500, "Failed to get price analytics");
        }
    }

    private string GetUserId()
    {
        // Try both standard JWT "sub" claim and ASP.NET Core NameIdentifier claim
        var userId = User.FindFirst("sub")?.Value ?? User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            _logger.LogWarning("User ID not found in JWT token. Available claims: {Claims}",
                string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));
            throw new UnauthorizedAccessException("User ID not found in token");
        }
        _logger.LogDebug("Successfully extracted user ID: {UserId}", userId);
        return userId;
    }

    private Guid GetUserIdAsGuid()
    {
        var userIdString = GetUserId();
        if (Guid.TryParse(userIdString, out var userIdGuid))
        {
            return userIdGuid;
        }
        throw new UnauthorizedAccessException($"User ID is not a valid GUID format: {userIdString}");
    }
}
