using System.IdentityModel.Tokens.Jwt;
using System.Text;
using MarketDataService.Brokers;
using MarketDataService.Configuration;
using MarketDataService.Data;
using MarketDataService.Interfaces;
using MarketDataService.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using StackExchange.Redis;
using DotNetEnv;

// Load .env file BEFORE creating the WebApplicationBuilder
// This ensures environment variables are available during configuration
var envPaths = new[]
{
    "../.env",           // Root level (development)
    ".env",              // Current directory (development)
    "../../.env",        // Parent directory (development)
    "/app/.env",         // Container root (production)
    "/src/.env"          // Alternative container path
};

foreach (var envPath in envPaths)
{
    if (File.Exists(envPath))
    {
        DotNetEnv.Env.Load(envPath);
        Console.WriteLine($"DEBUG: Loaded .env from: {envPath}");
        break;
    }
}

var builder = WebApplication.CreateBuilder(args);

Console.WriteLine("DEBUG: Using ASP.NET Core configuration with .env file support");

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddDebug();
}

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Market Data Service API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            new string[] {}
        }
    });
});

// Add health checks
builder.Services.AddHealthChecks();

// Configure database context using ASP.NET Core configuration
// Environment variables automatically override appsettings.json values
var connectionString = builder.Configuration["SUPABASE_CONNECTION_STRING"]
    ?? builder.Configuration.GetConnectionString("DefaultConnection")
    ?? throw new InvalidOperationException("No database connection string found. Set SUPABASE_CONNECTION_STRING environment variable or DefaultConnection in appsettings.json");

Console.WriteLine($"DEBUG: Using connection string from ASP.NET Core configuration");
Console.WriteLine($"DEBUG: Connection string: {connectionString}");
Console.WriteLine($"DEBUG: Connection string length: {connectionString.Length}");
Console.WriteLine($"DEBUG: Connection string ends with: '{connectionString.Substring(Math.Max(0, connectionString.Length - 20))}'");

// Only register PostgreSQL if not in testing environment (tests will register their own InMemory database)
if (!builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddDbContext<MarketDataContext>(options =>
    {
        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            npgsqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(10),
                errorCodesToAdd: null);
            npgsqlOptions.CommandTimeout(30);
        });

        if (builder.Environment.IsDevelopment())
        {
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        }
    });
}

// Configure settings
builder.Services.Configure<BrokerSettings>(builder.Configuration.GetSection("BrokerSettings"));
builder.Services.Configure<BrokerApiKeys>(builder.Configuration.GetSection("BrokerApiKeys"));

// Configure Redis connection (with fallback for development)
if (builder.Environment.IsDevelopment())
{
    // Use in-memory cache for development when Redis is not available
    builder.Services.AddMemoryCache();
    builder.Services.AddSingleton<IRedisCacheService, InMemoryCacheService>();
    // Add structured cache service for application-level caching
    builder.Services.AddScoped<ICacheService, MemoryCacheService>();
}
else
{
    builder.Services.AddSingleton<IConnectionMultiplexer>(
        _ => ConnectionMultiplexer.Connect(builder.Configuration["Redis:ConnectionString"] ?? "localhost:6379"));

    // Register cache services
    builder.Services.AddSingleton<RedisCacheService>();
    builder.Services.AddSingleton<IRedisCacheService>(provider => provider.GetRequiredService<RedisCacheService>());
    // Add structured cache service for application-level caching (fallback to memory cache in production too)
    builder.Services.AddMemoryCache();
    builder.Services.AddScoped<ICacheService, MemoryCacheService>();
}

// Register business services
if (builder.Environment.IsDevelopment())
{
    // Use real services with Supabase database, but mock external price providers
    builder.Services.AddScoped<WatchlistService>();
    builder.Services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
    builder.Services.AddScoped<BulkPriceService>();
    builder.Services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());

    // Mock price service for development (since we don't have real API keys)
    builder.Services.AddScoped<IPriceService, MockPriceService>();
    builder.Services.AddScoped<IHistoricalPriceRegistry, MockHistoricalPriceRegistry>();
}
else
{
    // Production services
    builder.Services.AddScoped<PriceService>();
    builder.Services.AddScoped<IPriceService>(provider => provider.GetRequiredService<PriceService>());
    builder.Services.AddScoped<WatchlistService>();
    builder.Services.AddScoped<IWatchlistService>(provider => provider.GetRequiredService<WatchlistService>());
    builder.Services.AddScoped<BulkPriceService>();
    builder.Services.AddScoped<IBulkPriceService>(provider => provider.GetRequiredService<BulkPriceService>());
}

// Add memory cache for bulk price operations
builder.Services.AddMemoryCache();

// Add response caching
builder.Services.AddResponseCaching();

// Register external data providers
builder.Services.AddHttpClient<FinnhubProvider>();
builder.Services.AddHttpClient<PolygonProvider>();

// Register FinnhubProvider for all interfaces
builder.Services.AddSingleton<IPriceProvider, FinnhubProvider>();
builder.Services.AddSingleton<IHistoricalPriceProvider, FinnhubProvider>();
builder.Services.AddSingleton<IBrokerMetadataProvider, FinnhubProvider>();
builder.Services.AddSingleton<ISymbolProvider, FinnhubProvider>();

// Register PolygonProvider for all interfaces
builder.Services.AddSingleton<IPriceProvider, PolygonProvider>();
builder.Services.AddSingleton<IHistoricalPriceProvider, PolygonProvider>();
builder.Services.AddSingleton<IBrokerMetadataProvider, PolygonProvider>();
builder.Services.AddSingleton<ISymbolProvider, PolygonProvider>();

// Register registries
builder.Services.AddSingleton<BrokerRegistry>();
builder.Services.AddSingleton<IBrokerRegistry>(provider => provider.GetRequiredService<BrokerRegistry>());
builder.Services.AddSingleton<HistoricalPriceRegistry>();
builder.Services.AddSingleton<IHistoricalPriceRegistry>(provider => provider.GetRequiredService<HistoricalPriceRegistry>());
builder.Services.AddSingleton<BrokerMetadataRegistry>();
builder.Services.AddSingleton<IBrokerMetadataRegistry>(provider => provider.GetRequiredService<BrokerMetadataRegistry>());
builder.Services.AddSingleton<SymbolRegistry>();
builder.Services.AddSingleton<ISymbolRegistry>(provider => provider.GetRequiredService<SymbolRegistry>());

// Auth (Supabase JWT) - Load from environment variables
// Environment variables automatically override appsettings.json values
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];

if (string.IsNullOrEmpty(supabaseUrl))
{
    throw new InvalidOperationException("SUPABASE_URL must be configured");
}

if (string.IsNullOrEmpty(supabaseProjectId))
{
    throw new InvalidOperationException("SUPABASE_PROJECT_ID must be configured");
}

JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        // Configure for Supabase RS256 JWT validation using JWKS endpoint
        options.Authority = $"{supabaseUrl}/auth/v1";
        options.Audience = supabaseProjectId;
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.FromMinutes(5),
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            // Let the JWKS endpoint handle key resolution for RS256 tokens
            IssuerSigningKeyResolver = null
        };

        // Add event handlers for debugging and better error responses
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Authentication failed in MarketData service: {Error} | Exception Type: {ExceptionType}",
                    context.Exception.Message, context.Exception.GetType().Name);

                // Log additional details for debugging
                if (context.Exception.InnerException != null)
                {
                    logger.LogWarning("Inner exception: {InnerError}", context.Exception.InnerException.Message);
                }

                // Set custom response for authentication failures
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";

                var errorResponse = new
                {
                    error = "Authentication Failed",
                    message = "Unable to validate RS256 JWT token from Supabase",
                    details = GetAuthenticationErrorDetails(context.Exception),
                    timestamp = DateTime.UtcNow,
                    service = "MarketData",
                    hint = "Ensure the token was issued by the Supabase auth service and is still valid"
                };

                var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                return context.Response.WriteAsync(json);
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                var userId = context.Principal?.FindFirst("sub")?.Value;
                logger.LogInformation("JWT Token validated in MarketData service for user: {UserId}", userId);
                return Task.CompletedTask;
            },
            OnChallenge = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Challenge triggered in MarketData service: {Error}", context.Error);

                // Skip the default challenge behavior
                context.HandleResponse();

                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";

                var errorResponse = new
                {
                    error = "Unauthorized",
                    message = "Valid JWT token required",
                    details = "Please ensure you have a valid authentication token from the auth service",
                    timestamp = DateTime.UtcNow,
                    service = "MarketData"
                };

                var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                return context.Response.WriteAsync(json);
            }
        };
    });

var app = builder.Build();

// Test database connection during startup (skip in testing environment)
if (!app.Environment.IsEnvironment("Testing"))
{
    await TestDatabaseConnection(app.Services);
}

// Skip database creation since we're using existing Supabase tables

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Market Data Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseCors("AllowAll");
app.UseResponseCaching();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Map health check endpoint
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in MarketData service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Market Data Service",
    version = "1.0.0",
    endpoints = new
    {
        prices = new[]
        {
            "GET /api/prices/{symbol} - Get current price for a symbol",
            "GET /api/prices/bulk - Get bulk prices for multiple symbols"
        },
        historical = new[]
        {
            "GET /api/historical/{symbol} - Get historical data for a symbol"
        },
        watchlist = new[]
        {
            "GET /api/watchlist - Get user's watchlist",
            "POST /api/watchlist - Add symbol to watchlist",
            "DELETE /api/watchlist/{symbol} - Remove symbol from watchlist"
        },
        brokers = new[]
        {
            "GET /api/brokers - Get available brokers",
            "GET /api/brokers/{brokerId}/metadata - Get broker metadata"
        },
        symbols = new[]
        {
            "GET /api/symbols - Get all symbols from all brokers",
            "GET /api/symbols/market/{marketType} - Get symbols by market type (stocks, forex, crypto)",
            "GET /api/symbols/active - Get most active/traded symbols",
            "GET /api/symbols/search - Search symbols by query",
            "GET /api/symbols/brokers - Get available brokers for symbol fetching",
            "POST /api/symbols/search - Bulk symbol search",
            "POST /api/symbols/active - Bulk get most active symbols"
        },
        health = new[]
        {
            "GET /health - Health check"
        }
    }
});

app.Run();



// Test database connection during startup
static async Task TestDatabaseConnection(IServiceProvider services)
{
    using var scope = services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<MarketDataContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Testing database connection...");

        // Test basic connectivity
        var canConnect = await context.Database.CanConnectAsync();
        if (canConnect)
        {
            logger.LogInformation("✅ Database connection successful!");

            // Test if tables exist with timeout protection
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

                logger.LogInformation("Testing watchlists table access...");
                var watchlistCount = await context.Watchlists.CountAsync(cts.Token);
                logger.LogInformation("✅ Watchlists table accessible: {WatchlistCount} records", watchlistCount);

                logger.LogInformation("Testing watchlist_symbols table access...");
                var symbolCount = await context.WatchlistItems.CountAsync(cts.Token);
                logger.LogInformation("✅ WatchlistItems table accessible: {SymbolCount} records", symbolCount);

                logger.LogInformation("✅ All tables accessible - Watchlists: {WatchlistCount}, Symbols: {SymbolCount}",
                    watchlistCount, symbolCount);
            }
            catch (OperationCanceledException)
            {
                logger.LogWarning("⚠️ Table access timed out - tables may not exist or network is slow");
                logger.LogInformation("Service will continue but database operations may fail.");
            }
            catch (Exception ex)
            {
                logger.LogWarning("⚠️ Tables may not exist yet: {Error}", ex.Message);
                logger.LogInformation("This is normal if tables haven't been created in Supabase yet.");

                // Try to check if it's a table name issue
                if (ex.Message.Contains("relation") && ex.Message.Contains("does not exist"))
                {
                    logger.LogInformation("💡 Hint: Make sure the tables 'watchlists' and 'watchlist_symbols' exist in your Supabase database");
                }
            }
        }
        else
        {
            logger.LogError("❌ Cannot connect to database");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "❌ Database connection test failed: {Error}", ex.Message);
        logger.LogWarning("Service will continue but database operations may fail.");
    }
}

// Helper method for authentication error details
static string GetAuthenticationErrorDetails(Exception exception)
{
    return exception switch
    {
        Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException => "Token has expired. Please login again to get a fresh token.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException => "Token signature validation failed. The token may be corrupted or not issued by Supabase.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException => "Token issuer is invalid. Expected issuer from Supabase auth service.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException => "Token audience is invalid. Expected audience matching Supabase project ID.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException tokenEx when tokenEx.Message.Contains("Unable to obtain configuration") =>
            "Unable to fetch JWKS configuration from Supabase. Check network connectivity and Supabase URL configuration.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException tokenEx when tokenEx.Message.Contains("IDX10501") =>
            "Token signature validation failed. Unable to match a key from JWKS endpoint.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException => "RS256 token validation failed. Ensure the token was issued by Supabase auth service.",
        InvalidOperationException opEx when opEx.Message.Contains("IDX20803") =>
            "Unable to obtain configuration from JWKS endpoint. Check Supabase URL and network connectivity.",
        _ => $"Authentication failed: {exception.Message}. Please ensure you're using a valid RS256 token from Supabase."
    };
}

// Make the implicit Program class public for testing
public partial class Program { }