@MarketDataService_HostAddress = http://localhost:5059
@jwt_token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.test-signature

### Health Check
GET {{MarketDataService_HostAddress}}/health
Accept: application/json

### Test JWT Authentication (requires valid token)
GET {{MarketDataService_HostAddress}}/api/watchlist/test-auth
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Test JWT Authentication (no token - should fail)
GET {{MarketDataService_HostAddress}}/api/watchlist/test-auth
Accept: application/json

### Get User Watchlists
GET {{MarketDataService_HostAddress}}/api/watchlist
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Get Default Watchlist
GET {{MarketDataService_HostAddress}}/api/watchlist/default
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Create a New Watchlist
POST {{MarketDataService_HostAddress}}/api/watchlist
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "name": "My Tech Stocks",
  "isDefault": true,
  "symbols": ["AAPL", "GOOGL", "MSFT", "TSLA"]
}

### Get Specific Watchlist
GET {{MarketDataService_HostAddress}}/api/watchlist/1
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Get Watchlist with Prices
GET {{MarketDataService_HostAddress}}/api/watchlist/1/with-prices
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Add Symbols to Watchlist
POST {{MarketDataService_HostAddress}}/api/watchlist/1/symbols
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "symbols": ["NVDA", "AMD"]
}

### Remove Symbol from Watchlist
DELETE {{MarketDataService_HostAddress}}/api/watchlist/1/symbols/TSLA
Authorization: Bearer {{jwt_token}}

### Update Watchlist
PUT {{MarketDataService_HostAddress}}/api/watchlist/1
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "name": "Updated Tech Stocks",
  "isDefault": true
}

### Reorder Watchlist Items
PUT {{MarketDataService_HostAddress}}/api/watchlist/1/reorder
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "items": [
    {"id": 1, "sortOrder": 1},
    {"id": 2, "sortOrder": 2},
    {"id": 3, "sortOrder": 3}
  ]
}

### Delete Watchlist
DELETE {{MarketDataService_HostAddress}}/api/watchlist/1
Authorization: Bearer {{jwt_token}}

### Get Bulk Prices
GET {{MarketDataService_HostAddress}}/api/prices/bulk?symbols=AAPL,GOOGL,MSFT
Authorization: Bearer {{jwt_token}}
Accept: application/json

### Get Single Price
GET {{MarketDataService_HostAddress}}/api/prices/AAPL
Authorization: Bearer {{jwt_token}}
Accept: application/json

###
