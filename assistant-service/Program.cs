using AssistantService.Services;
using AssistantService.Middleware;
using AssistantService.Configuration;
using AssistantService.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HealthChecks.NpgSql;

using DotNetEnv;

var builder = WebApplication.CreateBuilder(args);

// Load .env file from root directory for local development
if (builder.Environment.IsDevelopment())
{
    Env.Load("../.env");
}

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddDebug();
}

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.AddControllers()
    .ConfigureApiBehaviorOptions(options =>
    {
        // Customize model validation error responses
        options.InvalidModelStateResponseFactory = context =>
        {
            var errors = context.ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .SelectMany(x => x.Value!.Errors)
                .Select(x => x.ErrorMessage)
                .ToArray();

            var errorMessage = errors.Length > 0 ? errors[0] : "Invalid input";
            return new BadRequestObjectResult(errorMessage);
        };
    });

builder.Services.ConfigureHttpJsonOptions(options =>
{
    // Make JSON property names case-insensitive
    options.SerializerOptions.PropertyNameCaseInsensitive = true;
});

// Add memory cache for rate limiting and security services
builder.Services.AddMemoryCache();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();

// Configure security options
builder.Services.Configure<SecurityOptions>(builder.Configuration.GetSection(SecurityOptions.SectionName));

// Register security services
builder.Services.AddScoped<IPromptInjectionDetector, PromptInjectionDetector>();
builder.Services.AddScoped<IContentFilter, ContentFilter>();
builder.Services.AddSingleton<IRateLimitingService, RateLimitingService>();

builder.Services.AddHttpClient();
builder.Services.AddScoped<IAIClient, AIClient>();

// Configure JWT Authentication (Supabase)
var supabaseUrl = builder.Configuration["SUPABASE_URL"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];

bool hasValidSupabaseConfig = !string.IsNullOrEmpty(supabaseUrl) && !string.IsNullOrEmpty(supabaseProjectId);

// Only require Supabase configuration if not in testing environment
if (!hasValidSupabaseConfig && !builder.Environment.IsEnvironment("Testing"))
{
    throw new InvalidOperationException("SUPABASE_URL and SUPABASE_PROJECT_ID must be configured for JWT authentication");
}

// Configure JWT Authentication only if not in testing environment
if (!builder.Environment.IsEnvironment("Testing") && hasValidSupabaseConfig)
{
    // Clear default claim type mappings to use original claim names
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();

    builder.Services.AddAuthentication(Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            // Configure for Supabase RS256 JWT validation using JWKS endpoint
            options.Authority = $"{supabaseUrl}/auth/v1";
            options.Audience = supabaseProjectId;
            options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
            options.SaveToken = true;

            options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ClockSkew = TimeSpan.FromMinutes(5),
                RequireExpirationTime = true,
                RequireSignedTokens = true,
                // Let the JWKS endpoint handle key resolution for RS256 tokens
                IssuerSigningKeyResolver = null
            };

            // Add event handlers for debugging and better error responses
            options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogWarning("JWT Authentication failed in Assistant service: {Error} | Exception Type: {ExceptionType}",
                        context.Exception.Message, context.Exception.GetType().Name);

                    // Set custom response for authentication failures
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "application/json";

                    var errorResponse = new
                    {
                        error = "Authentication Failed",
                        message = "Unable to validate RS256 JWT token from Supabase",
                        details = GetAuthenticationErrorDetails(context.Exception),
                        timestamp = DateTime.UtcNow,
                        service = "Assistant"
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                    return context.Response.WriteAsync(json);
                },
                OnTokenValidated = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    var userId = context.Principal?.FindFirst("sub")?.Value;
                    logger.LogInformation("JWT Token validated in Assistant service for user: {UserId}", userId);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                    logger.LogWarning("JWT Challenge triggered in Assistant service: {Error}", context.Error);

                    // Skip the default challenge behavior
                    context.HandleResponse();

                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "application/json";

                    var errorResponse = new
                    {
                        error = "Unauthorized",
                        message = "Valid JWT token required",
                        details = "Please ensure you have a valid authentication token from the auth service",
                        timestamp = DateTime.UtcNow,
                        service = "Assistant"
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                    return context.Response.WriteAsync(json);
                }
            };
        });

    builder.Services.AddAuthorization();
}

// Configure Entity Framework with Supabase PostgreSQL
// Only register PostgreSQL if not in testing environment (tests will register their own InMemory database)
if (!builder.Environment.IsEnvironment("Testing"))
{
var connectionString = Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") == "Development"
    ? builder.Configuration.GetValue<string>("LocalSecrets:SUPABASE_CONNECTION_STRING") // from local secrets or .env
    : Environment.GetEnvironmentVariable("SUPABASE_CONNECTION_STRING") // from GitHub secrets or environment variables in production
    ?? builder.Configuration.GetConnectionString("DefaultConnection")
    ?? throw new InvalidOperationException("No database connection string found. Set SUPABASE_CONNECTION_STRING environment variable or DefaultConnection in appsettings.json");

    builder.Services.AddDbContext<AssistantDbContext>(options =>
    {
        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            npgsqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorCodesToAdd: null);
        });

        // Enable sensitive data logging in development
        if (builder.Environment.IsDevelopment())
        {
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        }
    });
}

// Register chat storage service
builder.Services.AddScoped<IChatStorageService, ChatStorageService>();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Assistant Service API", Version = "v1" });
});

builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Service is running"))
    .AddCheck("memory", () =>
    {
        var allocatedBytes = GC.GetTotalMemory(false);
        var maxBytes = 1024 * 1024 * 1024; // 1GB threshold
        return allocatedBytes < maxBytes
            ? Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy($"Memory usage: {allocatedBytes / 1024 / 1024} MB")
            : Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy($"High memory usage: {allocatedBytes / 1024 / 1024} MB");
    });

if (!builder.Environment.IsEnvironment("Testing"))
{
    var connectionString = builder.Configuration["SUPABASE_CONNECTION_STRING"]
        ?? builder.Configuration.GetConnectionString("DefaultConnection");

    if (!string.IsNullOrEmpty(connectionString))
    {
        builder.Services.AddHealthChecks()
            .AddNpgSql(connectionString, name: "database", timeout: TimeSpan.FromSeconds(30));
    }
}

var app = builder.Build();

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Assistant Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// Add authentication and authorization middleware
if (hasValidSupabaseConfig)
{
    app.UseAuthentication();
    app.UseAuthorization();
}

// Apply security middleware early in the pipeline
app.UseSecurityMiddleware();

app.MapControllers();

// Map health check endpoint
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Assistant Service",
    version = "1.0.0",
    endpoints = new
    {
        assistant = new[]
        {
            "POST /api/assistant/ask - Ask the AI assistant",
            "POST /api/chat - Chat with the assistant"
        },
        chatHistory = new[]
        {
            "POST /api/chathistory/history - Get chat history with filters",
            "GET /api/chathistory/sessions - Get chat sessions",
            "GET /api/chathistory/sessions/{sessionId}/messages - Get messages for a session",
            "DELETE /api/chathistory/messages/{messageId} - Delete a message",
            "DELETE /api/chathistory/sessions/{sessionId} - Delete a session",
            "PUT /api/chathistory/sessions/{sessionId}/title - Update conversation title"
        },
        health = new[]
        {
            "GET /health - Health check"
        }
    }
});

// Helper method for authentication error details
static string GetAuthenticationErrorDetails(Exception exception)
{
    return exception switch
    {
        Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException => "Token has expired. Please login again to get a fresh token.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException => "Token signature validation failed. The token may be corrupted or not issued by Supabase.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException => "Token issuer is invalid. Expected issuer from Supabase auth service.",
        Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException => "Token audience is invalid. Expected audience matching Supabase project ID.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException tokenEx when tokenEx.Message.Contains("Unable to obtain configuration") =>
            "Unable to fetch JWKS configuration from Supabase. Check network connectivity and Supabase URL configuration.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException tokenEx when tokenEx.Message.Contains("IDX10501") =>
            "Token signature validation failed. Unable to match a key from JWKS endpoint.",
        Microsoft.IdentityModel.Tokens.SecurityTokenValidationException => "RS256 token validation failed. Ensure the token was issued by Supabase auth service.",
        InvalidOperationException opEx when opEx.Message.Contains("IDX20803") =>
            "Unable to obtain configuration from JWKS endpoint. Check Supabase URL and network connectivity.",
        _ => $"Authentication failed: {exception.Message}. Please ensure you're using a valid RS256 token from Supabase."
    };
}

app.Run();

// Make the implicit Program class public for testing
public partial class Program { }
