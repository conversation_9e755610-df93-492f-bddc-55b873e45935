using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using AssistantService.Models;
using AssistantService.Services;
using AssistantService.Configuration;

namespace AssistantService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AssistantController(IAIClient aiClient, IPromptInjectionDetector injectionDetector, IContentFilter contentFilter, IOptions<SecurityOptions> securityOptions, IChatStorageService chatStorage) : ControllerBase
{
    private readonly IAIClient _aiClient = aiClient;
    private readonly IPromptInjectionDetector _injectionDetector = injectionDetector;
    private readonly IContentFilter _contentFilter = contentFilter;
    private readonly SecurityOptions _securityOptions = securityOptions.Value;
    private readonly IChatStorageService _chatStorage = chatStorage;

    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        // Validate input
        if (request == null)
        {
            return BadRequest("Request cannot be empty");
        }

        string sanitizedPrompt = request.Prompt;

        // Check for prompt injection attacks (only if enabled)
        if (_securityOptions.EnablePromptInjectionDetection)
        {
            var injectionResult = _injectionDetector.AnalyzeInput(request.Prompt);

            if (injectionResult.RiskLevel == RiskLevel.Critical)
            {
                return BadRequest("Request blocked due to security concerns");
            }

            if (injectionResult.RiskLevel == RiskLevel.High)
            {
                return BadRequest("Request contains potentially harmful content");
            }

            sanitizedPrompt = injectionResult.SanitizedInput ?? request.Prompt;
        }

        // Apply content filtering (only if enabled)
        if (_securityOptions.EnableContentFiltering)
        {
            var contentResult = _contentFilter.FilterContent(sanitizedPrompt);

            if (!contentResult.IsContentSafe)
            {
                return BadRequest($"Content policy violation: {contentResult.Reason}");
            }

            sanitizedPrompt = contentResult.FilteredContent ?? string.Empty;
        }

        if (string.IsNullOrWhiteSpace(sanitizedPrompt))
        {
            return BadRequest("Prompt cannot be empty");
        }

        if (sanitizedPrompt.Length > _securityOptions.MaxInputLength)
        {
            return BadRequest($"Prompt is too long (max {_securityOptions.MaxInputLength} characters)");
        }

        try
        {
            // Generate session ID for this conversation
            var sessionId = Guid.NewGuid().ToString();

            // For now, use a default user ID since we don't have authentication
            // In production, this should come from the authenticated user
            var userId = Guid.Parse("00000000-0000-0000-0000-000000000001");

            // Save user message to database
            try
            {
                await _chatStorage.SaveMessageAsync(userId, new CreateChatMessageRequest
                {
                    Message = sanitizedPrompt,
                    Role = "user",
                    SessionId = sessionId,
                    Model = "mistral"
                });
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the request
                Console.WriteLine($"Failed to save user message: {ex.Message}");
            }

            // Determine AI provider to use, default to OpenAI if not specified
            var provider = request.Provider ?? AIProvider.OpenAI;

            // Get AI response with selected provider
            var reply = await _aiClient.AskAsync(sanitizedPrompt, provider);

            // Save AI response to database
            try
            {
                await _chatStorage.SaveMessageAsync(userId, new CreateChatMessageRequest
                {
                    Message = reply,
                    Role = "assistant",
                    SessionId = sessionId,
                    Model = provider.ToString().ToLower()
                });
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the request
                Console.WriteLine($"Failed to save AI response: {ex.Message}");
            }

            return Ok(new AskResponse { Reply = reply });
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(503, ex.Message);
        }
        catch (Exception)
        {
            return StatusCode(500, "Something went wrong. Please try again.");
        }
    }


}
