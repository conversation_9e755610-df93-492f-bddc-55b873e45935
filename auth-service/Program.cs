using AuthService.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

// ASP.NET Core automatically loads environment variables - no additional setup needed
Console.WriteLine("DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)");

// Load Supabase URL and Project ID from config
var supabaseUrl       = builder.Configuration["SUPABASE_URL"];
var supabaseProjectId = builder.Configuration["SUPABASE_PROJECT_ID"];

// Configure Authentication
builder.Services
    .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        // Point at Supabase's OIDC endpoint
        options.Authority = $"{supabaseUrl}/auth/v1";
        options.Audience  = supabaseProjectId;
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();

        // Validate issuer, audience, lifetime, and handle small clock drift
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer           = true,
            ValidateAudience         = true,
            ValidateLifetime         = true,
            ClockSkew                = TimeSpan.FromSeconds(30)
        };

        // Hook into events for visibility
        options.Events = new JwtBearerEvents
        {
            OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices
                    .GetRequiredService<ILogger<Program>>();
                logger.LogWarning("JWT Authentication failed: {Error}", context.Exception.Message);
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                var logger = context.HttpContext.RequestServices
                    .GetRequiredService<ILogger<Program>>();
                var userId = context.Principal?.FindFirst("sub")?.Value;
                logger.LogInformation("JWT validated for user: {UserId}", userId);
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddControllers();
builder.Services.AddAuthorization();

builder.Services.AddSwaggerGen();

// Register SupabaseClient and dependencies for ISupabaseClient
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();
builder.Services.AddScoped<ICacheService, MemoryCacheService>();
builder.Services.AddScoped<ISupabaseClient, SupabaseClient>();

builder.Services.AddHealthChecks();

var app = builder.Build();

// Test comment for deployment detection

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auth Service API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Add error handling endpoint
app.Map("/error", (ILogger<Program> logger) =>
{
    logger.LogError("An unhandled exception occurred in Auth service");
    return Results.Problem("An error occurred while processing your request.");
});

// Add a root endpoint for API information
app.MapGet("/", () => new
{
    service = "Auth Service",
    version = "1.0.0",
    endpoints = new
    {
        authentication = new[]
        {
            "POST /auth/signup - User signup",
            "POST /auth/login - User login",
            "POST /auth/token/refresh - Refresh access token",
            "POST /auth/recover - Password recovery",
            "POST /auth/otp - Send OTP",
            "POST /auth/verify - Verify OTP",
            "POST /auth/logout - User logout"
        },
        user_management = new[]
        {
            "GET /auth/user - Get current user",
            "PUT /auth/user - Update user",
            "GET /auth/profile - Get user profile",
            "POST /auth/profile/link-broker - Link broker to profile"
        },
        user_info = new[]
        {
            "GET /auth/user/me - Get current user from JWT claims",
            "GET /auth/user/profile - Get user profile",
            "GET /auth/user/role/{roleName} - Check if user has specific role",
            "GET /auth/user/trader-only - Trader-only endpoint (requires IsTrader policy)",
            "GET /auth/user/admin-only - Admin-only endpoint (requires IsAdmin policy)",
            "GET /auth/user/broker-required - Requires linked broker (HasBroker policy)"
        },
        oauth = new[]
        {
            "POST /auth/oauth - Get OAuth URL",
            "POST /auth/oauth/callback - OAuth callback"
        },
        settings = new[]
        {
            "GET /auth/settings - Get auth settings"
        },
        health = new[]
        {
            "GET /health - Health check"
        }
    }
});

app.Run();

// Make the implicit Program class public for testing
public partial class Program { }
